<?php 
// application/core/MY_Loader.php

class MY_Loader extends CI_Loader {
    
    public function __construct() {
        parent::__construct();
    }
    
    public function middleware($middleware_name) {
        $file = APPPATH . 'middleware/' . $middleware_name . '.php';
        
        if (file_exists($file)) {
            require_once($file);
            $middleware = new $middleware_name();
            return $middleware;
        } else {
            show_error('Unable to load middleware: ' . $middleware_name);
        }
    }

    public function validation($validation_name) {

        $CI =& get_instance();
        $original_name = $validation_name;
        $validation_name = strtolower($validation_name);

        // Try the original case first (e.g., AdvertisingValidator)
        if (file_exists(APPPATH . 'validation/' . $original_name . '.php')) {
            require_once(APPPATH . 'validation/' . $original_name . '.php');
            $CI->$validation_name = new $original_name();
            return;
        }

        // Try with ucfirst (e.g., Advertisingvalidator)
        $class = ucfirst($validation_name);
        if (file_exists(APPPATH . 'validation/' . $class . '.php')) {
            require_once(APPPATH . 'validation/' . $class . '.php');
            $CI->$validation_name = new $class();
            return;
        }

        throw new Exception("validation {$original_name} not found");

        // $file = APPPATH . 'validation/' . $validation_name . '.php';
        
        // if (file_exists($file)) {
        //     require_once($file);
        //     $validator = new $validation_name();
        //     return $validator;
        // } else {
        //     show_error('Unable to load validation: ' . $validation_name);
        // }
    }

    public function service($service_name) {
        $CI =& get_instance();
        // $service_name = strtolower($service_name);
        $class = ucfirst($service_name);
       
        if (!file_exists(APPPATH . 'services/' . $class . '.php')) {
            throw new Exception("Service {$class} not found");
        }
        
        require_once(APPPATH . 'services/' . $class . '.php');
        $CI->$service_name = new $class();
    }
}